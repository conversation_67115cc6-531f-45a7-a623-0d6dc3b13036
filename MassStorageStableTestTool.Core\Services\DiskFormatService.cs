using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Security.Principal;
using System.Text;

namespace MassStorageStableTestTool.Core.Services;

/// <summary>
/// 磁盘格式化服务实现
/// </summary>
public class DiskFormatService : IDiskFormatService
{
    private readonly ILogger<DiskFormatService> _logger;
    private readonly List<string> _supportedFileSystems = new() { "FAT32", "NTFS", "exFAT" };

    public DiskFormatService(ILogger<DiskFormatService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 格式化磁盘
    /// </summary>
    public async Task<FormatResult> FormatDriveAsync(
        string driveLetter,
        string? fileSystem = null,
        string? volumeLabel = null,
        bool quickFormat = true,
        CancellationToken cancellationToken = default,
        IProgress<ProgressEventArgs>? progress = null)
    {
        var result = new FormatResult();
        var startTime = DateTime.Now;

        try
        {
            // 验证参数
            if (!ValidateDriveLetter(driveLetter))
            {
                return FormatResult.CreateFailure($"无效的驱动器盘符: {driveLetter}");
            }

            // 获取驱动器当前信息
            var driveInfo = new System.IO.DriveInfo(driveLetter);
            if (!driveInfo.IsReady)
            {
                return FormatResult.CreateFailure($"驱动器 {driveLetter} 未就绪");
            }

            // 如果未指定文件系统，使用当前文件系统
            if (string.IsNullOrEmpty(fileSystem))
            {
                fileSystem = driveInfo.DriveFormat;
                _logger.LogInformation("未指定文件系统，使用当前文件系统: {FileSystem}", fileSystem);
            }

            // 如果未指定卷标，使用当前卷标
            if (string.IsNullOrEmpty(volumeLabel))
            {
                volumeLabel = driveInfo.VolumeLabel;
                _logger.LogInformation("未指定卷标，使用当前卷标: {VolumeLabel}", volumeLabel);
            }

            _logger.LogInformation("开始格式化驱动器 {DriveLetter}，文件系统: {FileSystem}，卷标: {VolumeLabel}",
                driveLetter, fileSystem, volumeLabel);

            result.AddLog($"开始格式化驱动器 {driveLetter}，文件系统: {fileSystem}，卷标: {volumeLabel}");

            if (!IsFileSystemSupported(fileSystem))
            {
                return FormatResult.CreateFailure($"不支持的文件系统: {fileSystem}");
            }

            // 检查是否有管理员权限
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows) && !IsRunningAsAdministrator())
            {
                return FormatResult.CreateFailure("格式化驱动器需要管理员权限，请以管理员身份运行程序");
            }

            // 检查驱动器是否可以格式化
            progress?.Report(new ProgressEventArgs { Progress = 10, Status = "检查驱动器..." });
            var (canFormat, issues) = await CheckDriveFormatabilityAsync(driveLetter);
            if (!canFormat)
            {
                return FormatResult.CreateFailure($"驱动器不能格式化: {string.Join(", ", issues)}");
            }

            // 执行格式化
            progress?.Report(new ProgressEventArgs { Progress = 20, Status = "正在格式化..." });

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                // 首先尝试使用format命令
                var formatSuccess = await FormatDriveWindowsAsync(driveLetter, fileSystem, volumeLabel, quickFormat, cancellationToken, progress);
                if (!formatSuccess.Success)
                {
                    _logger.LogWarning("使用format命令格式化失败，尝试使用PowerShell方法: {Error}", formatSuccess.ErrorMessage);

                    // 如果format命令失败，尝试使用PowerShell
                    formatSuccess = await FormatDrivePowerShellAsync(driveLetter, fileSystem, volumeLabel, quickFormat, cancellationToken, progress);
                    if (!formatSuccess.Success)
                    {
                        return FormatResult.CreateFailure($"格式化失败 - format命令错误: {formatSuccess.ErrorMessage}; PowerShell错误: {formatSuccess.ErrorMessage}");
                    }
                    
                }
                
                // 验证格式化结果
                progress?.Report(new ProgressEventArgs { Progress = 90, Status = "验证格式化结果..." });

                // 等待系统刷新，并进行重试验证
                const int maxVerifyRetries = 5;
                const int verifyDelayMs = 1000;
                bool driveReady = false;

                for (int attempt = 1; attempt <= maxVerifyRetries; attempt++)
                {
                    await Task.Delay(verifyDelayMs, cancellationToken);

                    try
                    {
                        driveInfo = new System.IO.DriveInfo(driveLetter);
                        if (driveInfo.IsReady)
                        {
                            driveReady = true;
                            _logger.LogDebug("驱动器 {Drive} 验证成功 (尝试 {Attempt}/{MaxRetries})",
                                driveLetter, attempt, maxVerifyRetries);
                            break;
                        }
                        else
                        {
                            _logger.LogDebug("驱动器 {Drive} 尚未就绪 (尝试 {Attempt}/{MaxRetries})",
                                driveLetter, attempt, maxVerifyRetries);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug("验证驱动器 {Drive} 状态时出错 (尝试 {Attempt}/{MaxRetries}): {Error}",
                            driveLetter, attempt, maxVerifyRetries, ex.Message);
                    }
                }

                if (!driveReady)
                {
                    return FormatResult.CreateFailure($"格式化完成但驱动器在 {maxVerifyRetries} 次验证后仍未就绪");
                }

                result.Success = true;
                result.FileSystem = driveInfo.DriveFormat;
                result.VolumeLabel = driveInfo.VolumeLabel;
                result.Duration = DateTime.Now - startTime;
                result.AddLog($"格式化完成，文件系统: {result.FileSystem}，卷标: {result.VolumeLabel}");

                progress?.Report(new ProgressEventArgs { Progress = 100, Status = "格式化完成" });

                _logger.LogInformation("驱动器 {DriveLetter} 格式化成功，耗时: {Duration}",
                    driveLetter, result.Duration);

                return result;
            }
            else
            {
                return FormatResult.CreateFailure("当前只支持Windows系统的磁盘格式化");
            }
        }
        catch (OperationCanceledException)
        {
            result.AddLog("格式化被取消");
            return FormatResult.CreateFailure("格式化被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "格式化驱动器 {DriveLetter} 失败", driveLetter);
            result.AddLog($"格式化失败: {ex.Message}");
            return FormatResult.CreateFailure($"格式化失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 检查驱动器是否可以安全格式化
    /// </summary>
    public async Task<(bool CanFormat, List<string> Issues)> CheckDriveFormatabilityAsync(string driveLetter)
    {
        var issues = new List<string>();

        try
        {
            if (!ValidateDriveLetter(driveLetter))
            {
                issues.Add($"无效的驱动器盘符: {driveLetter}");
                return (false, issues);
            }

            var driveInfo = new System.IO.DriveInfo(driveLetter);
            
            if (!driveInfo.IsReady)
            {
                issues.Add("驱动器未就绪");
                return (false, issues);
            }

            // 检查是否为可移动驱动器
            if (driveInfo.DriveType != DriveType.Removable)
            {
                issues.Add($"驱动器类型为 {driveInfo.DriveType}，只允许格式化可移动驱动器");
                return (false, issues);
            }

            // 检查是否为系统驱动器
            var systemDrive = Path.GetPathRoot(Environment.SystemDirectory);
            if (string.Equals(driveLetter.TrimEnd('\\'), systemDrive?.TrimEnd('\\'), StringComparison.OrdinalIgnoreCase))
            {
                issues.Add("不能格式化系统驱动器");
                return (false, issues);
            }

            // 检查驱动器是否被占用
            if (await IsDriveInUseAsync(driveLetter))
            {
                issues.Add("驱动器正在被其他程序使用");
                return (false, issues);
            }

            return (true, issues);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查驱动器 {DriveLetter} 可格式化性时出错", driveLetter);
            issues.Add($"检查驱动器时出错: {ex.Message}");
            return (false, issues);
        }
    }

    /// <summary>
    /// 获取支持的文件系统列表
    /// </summary>
    public List<string> GetSupportedFileSystems()
    {
        return new List<string>(_supportedFileSystems);
    }

    /// <summary>
    /// 验证文件系统类型是否受支持
    /// </summary>
    public bool IsFileSystemSupported(string fileSystem)
    {
        return _supportedFileSystems.Contains(fileSystem, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 在Windows系统上使用format命令格式化驱动器
    /// </summary>
    private async Task<FormatResult> FormatDriveWindowsAsync(
        string driveLetter,
        string fileSystem,
        string volumeLabel,
        bool quickFormat,
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null)
    {
        try
        {
            // 使用format命令进行格式化
            var formatArgs = BuildFormatCommand(driveLetter, fileSystem, volumeLabel, quickFormat);
            var fullCommand = $"cmd.exe /c echo Y | format {formatArgs}";

            _logger.LogDebug("执行格式化命令: {Command}", fullCommand);
            _logger.LogInformation("格式化参数 - 驱动器: {Drive}, 文件系统: {FileSystem}, 卷标: {VolumeLabel}, 快速格式化: {QuickFormat}",
                driveLetter, fileSystem, volumeLabel, quickFormat);

            var processStartInfo = new ProcessStartInfo
            {
                FileName = "cmd.exe",
                Arguments = $"/c echo Y | format {formatArgs}",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                RedirectStandardInput = false, // 不需要重定向输入，因为使用echo Y
                CreateNoWindow = true,
                StandardOutputEncoding = Encoding.UTF8,
                StandardErrorEncoding = Encoding.UTF8
            };

            using var process = new Process { StartInfo = processStartInfo };
            var output = new StringBuilder();
            var error = new StringBuilder();

            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    output.AppendLine(e.Data);
                    _logger.LogDebug("Format输出: {Output}", e.Data);
                    
                    // 尝试解析进度
                    if (e.Data.Contains("percent completed", StringComparison.OrdinalIgnoreCase))
                    {
                        var percentMatch = System.Text.RegularExpressions.Regex.Match(e.Data, @"(\d+)\s*percent");
                        if (percentMatch.Success && int.TryParse(percentMatch.Groups[1].Value, out var percent))
                        {
                            progress?.Report(new ProgressEventArgs 
                            { 
                                Progress = 20 + (percent * 60 / 100), 
                                Status = $"格式化进度: {percent}%" 
                            });
                        }
                    }
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    error.AppendLine(e.Data);
                    _logger.LogWarning("Format错误: {Error}", e.Data);
                }
            };

            process.Start();

            // 给进程更多时间完全启动和稳定
            await Task.Delay(500, cancellationToken);

            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            // 使用更稳健的等待逻辑：结合轮询和WaitForExitAsync
            try
            {
                _logger.LogDebug("开始等待格式化进程完成...");

                // 首先使用轮询方式等待，确保进程状态稳定
                var timeout = TimeSpan.FromMinutes(10); // 格式化超时时间
                var startTime = DateTime.Now;

                while (!process.HasExited && !cancellationToken.IsCancellationRequested)
                {
                    if (DateTime.Now - startTime > timeout)
                    {
                        _logger.LogError("格式化进程超时 ({Timeout} 分钟)", timeout.TotalMinutes);
                        process.Kill();
                        return FormatResult.CreateFailure($"格式化超时 ({timeout.TotalMinutes} 分钟)");
                    }

                    await Task.Delay(1000, cancellationToken);
                    _logger.LogDebug("等待格式化进程... (已等待 {Elapsed})", DateTime.Now - startTime);
                }

                if (cancellationToken.IsCancellationRequested)
                {
                    _logger.LogWarning("格式化进程被取消，正在终止进程...");
                    try
                    {
                        if (!process.HasExited)
                        {
                            process.Kill();
                            await Task.Delay(2000, CancellationToken.None); // 等待进程完全终止
                        }
                    }
                    catch (Exception killEx)
                    {
                        _logger.LogError(killEx, "终止格式化进程时出错");
                    }
                    return FormatResult.CreateFailure("格式化被取消");
                }

                // 进程已退出，再等待一下确保所有输出都被读取
                await Task.Delay(1000, CancellationToken.None);
                _logger.LogDebug("格式化进程已完成，退出码: {ExitCode}", process.ExitCode);
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("格式化进程等待被取消");
                try
                {
                    if (!process.HasExited)
                    {
                        process.Kill();
                        await Task.Delay(2000, CancellationToken.None);
                    }
                }
                catch (Exception killEx)
                {
                    _logger.LogError(killEx, "终止格式化进程时出错");
                }
                return FormatResult.CreateFailure("格式化被取消");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "等待格式化进程时出错");
                try
                {
                    if (!process.HasExited)
                    {
                        process.Kill();
                    }
                }
                catch (Exception killEx)
                {
                    _logger.LogError(killEx, "终止格式化进程时出错");
                }
                return FormatResult.CreateFailure($"等待格式化进程时出错: {ex.Message}");
            }

            if (process.ExitCode == 0)
            {
                return FormatResult.CreateSuccess(fileSystem, volumeLabel, TimeSpan.Zero);
            }
            else
            {
                var errorMessage = error.Length > 0 ? error.ToString() : "格式化失败";
                var outputMessage = output.Length > 0 ? output.ToString() : "无输出";

                _logger.LogError("格式化命令失败 - 退出码: {ExitCode}, 错误输出: {Error}, 标准输出: {Output}",
                    process.ExitCode, errorMessage, outputMessage);

                // 根据退出码提供具体的错误信息和建议
                var detailedError = GetFormatErrorDetails(process.ExitCode, errorMessage, outputMessage, driveLetter);

                return FormatResult.CreateFailure(detailedError);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行Windows格式化命令时出错");
            return FormatResult.CreateFailure($"执行格式化命令时出错: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 在Windows系统上使用PowerShell格式化驱动器
    /// </summary>
    private async Task<FormatResult> FormatDrivePowerShellAsync(
        string driveLetter,
        string fileSystem,
        string volumeLabel,
        bool quickFormat,
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null)
    {
        try
        {
            // 构建PowerShell命令
            var driveLetter_clean = driveLetter.TrimEnd('\\', '/', ':');
            var quickParam = quickFormat ? "-Quick" : "";
            var labelParam = !string.IsNullOrWhiteSpace(volumeLabel) ? $"-NewFileSystemLabel '{volumeLabel}'" : "";

            var powershellCommand = $"Format-Volume -DriveLetter {driveLetter_clean} -FileSystem {fileSystem} {quickParam} {labelParam} -Confirm:$false";

            _logger.LogDebug("执行PowerShell格式化命令: {Command}", powershellCommand);

            var processStartInfo = new ProcessStartInfo
            {
                FileName = "powershell.exe",
                Arguments = $"-Command \"{powershellCommand}\"",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                StandardOutputEncoding = Encoding.UTF8,
                StandardErrorEncoding = Encoding.UTF8
            };

            using var process = new Process { StartInfo = processStartInfo };
            var output = new StringBuilder();
            var error = new StringBuilder();

            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    output.AppendLine(e.Data);
                    _logger.LogDebug("PowerShell输出: {Output}", e.Data);
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    error.AppendLine(e.Data);
                    _logger.LogWarning("PowerShell错误: {Error}", e.Data);
                }
            };

            process.Start();

            // 给进程时间启动
            await Task.Delay(500, cancellationToken);

            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            // 使用稳健的等待逻辑
            try
            {
                _logger.LogDebug("开始等待PowerShell格式化进程完成...");

                var timeout = TimeSpan.FromMinutes(10);
                var startTime = DateTime.Now;

                while (!process.HasExited && !cancellationToken.IsCancellationRequested)
                {
                    if (DateTime.Now - startTime > timeout)
                    {
                        _logger.LogError("PowerShell格式化进程超时");
                        process.Kill();
                        return FormatResult.CreateFailure("PowerShell格式化超时");
                    }

                    await Task.Delay(1000, cancellationToken);
                    progress?.Report(new ProgressEventArgs { Progress = 50, Status = "PowerShell格式化中..." });
                }

                if (cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        if (!process.HasExited)
                        {
                            process.Kill();
                            await Task.Delay(2000, CancellationToken.None);
                        }
                    }
                    catch (Exception killEx)
                    {
                        _logger.LogError(killEx, "终止PowerShell格式化进程时出错");
                    }
                    return FormatResult.CreateFailure("PowerShell格式化被取消");
                }

                // 等待输出完全读取
                await Task.Delay(1000, CancellationToken.None);
                _logger.LogDebug("PowerShell格式化进程已完成，退出码: {ExitCode}", process.ExitCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "等待PowerShell格式化进程时出错");
                try
                {
                    if (!process.HasExited)
                    {
                        process.Kill();
                    }
                }
                catch (Exception killEx)
                {
                    _logger.LogError(killEx, "终止PowerShell格式化进程时出错");
                }
                return FormatResult.CreateFailure($"等待PowerShell格式化进程时出错: {ex.Message}");
            }

            if (process.ExitCode == 0)
            {
                return FormatResult.CreateSuccess(fileSystem, volumeLabel, TimeSpan.Zero);
            }
            else
            {
                var errorMessage = error.Length > 0 ? error.ToString() : "PowerShell格式化失败";
                var outputMessage = output.Length > 0 ? output.ToString() : "无输出";

                _logger.LogError("PowerShell格式化命令失败 - 退出码: {ExitCode}, 错误输出: {Error}, 标准输出: {Output}",
                    process.ExitCode, errorMessage, outputMessage);

                return FormatResult.CreateFailure($"PowerShell格式化命令失败 (退出码: {process.ExitCode}): {errorMessage}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行PowerShell格式化命令时出错");
            return FormatResult.CreateFailure($"执行PowerShell格式化命令时出错: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 根据format命令的退出码提供详细的错误信息和解决建议
    /// </summary>
    private string GetFormatErrorDetails(int exitCode, string errorMessage, string outputMessage, string driveLetter)
    {
        var details = new StringBuilder();
        details.AppendLine($"格式化命令失败 (退出码: {exitCode})");

        switch (exitCode)
        {
            case 1:
                details.AppendLine("原因: 命令参数错误");
                details.AppendLine("建议: 检查驱动器盘符和文件系统参数是否正确");
                break;

            case 4:
                details.AppendLine("原因: 致命错误 - 驱动器无法格式化");
                details.AppendLine("可能的原因:");
                details.AppendLine("  • 驱动器被其他程序锁定或正在使用");
                details.AppendLine("  • 驱动器硬件故障或连接问题");
                details.AppendLine("  • 文件系统严重损坏");
                details.AppendLine("  • 系统保护阻止格式化");
                details.AppendLine("建议:");
                details.AppendLine("  1. 关闭所有可能使用该驱动器的程序");
                details.AppendLine("  2. 重新插拔驱动器");
                details.AppendLine("  3. 尝试在磁盘管理中格式化");
                details.AppendLine("  4. 检查驱动器健康状态");
                break;

            case 5:
                details.AppendLine("原因: 访问被拒绝");
                details.AppendLine("建议: 确保程序以管理员身份运行，并检查驱动器权限");
                break;

            default:
                details.AppendLine($"原因: 未知错误 (退出码: {exitCode})");
                details.AppendLine("建议: 检查系统日志获取更多信息");
                break;
        }

        if (!string.IsNullOrEmpty(errorMessage) && errorMessage != "格式化失败")
        {
            details.AppendLine($"错误详情: {errorMessage.Trim()}");
        }

        if (!string.IsNullOrEmpty(outputMessage) && outputMessage != "无输出")
        {
            details.AppendLine($"命令输出: {outputMessage.Trim()}");
        }

        return details.ToString();
    }

    /// <summary>
    /// 构建格式化命令参数
    /// </summary>
    private string BuildFormatCommand(string driveLetter, string fileSystem, string volumeLabel, bool quickFormat)
    {
        var args = new StringBuilder();
        
        // 驱动器盘符
        args.Append($"{driveLetter.TrimEnd('\\', '/', ':')}: ");
        
        // 文件系统
        args.Append($"/FS:{fileSystem} ");
        
        // 卷标
        if (!string.IsNullOrWhiteSpace(volumeLabel))
        {
            args.Append($"/V:\"{volumeLabel}\" ");
        }
        
        // 快速格式化
        if (quickFormat)
        {
            args.Append("/Q ");
        }
        
        // 自动确认（通过cmd执行时使用echo命令）
        // args.Append("/Y"); // 注释掉，因为通过cmd执行时会用echo来自动确认
        
        return args.ToString();
    }

    /// <summary>
    /// 验证驱动器盘符格式
    /// </summary>
    private bool ValidateDriveLetter(string driveLetter)
    {
        if (string.IsNullOrWhiteSpace(driveLetter))
            return false;

        var cleanDrive = driveLetter.Trim().TrimEnd('\\', '/');
        if (!cleanDrive.EndsWith(':'))
            cleanDrive += ":";

        return cleanDrive.Length == 2 && char.IsLetter(cleanDrive[0]);
    }

    /// <summary>
    /// 检查驱动器是否正在被使用
    /// </summary>
    private async Task<bool> IsDriveInUseAsync(string driveLetter)
    {
        const int maxRetries = 3;
        const int retryDelayMs = 500;

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                // 简单检查：尝试创建一个临时文件
                var testFile = Path.Combine(driveLetter, $"test_{Guid.NewGuid():N}.tmp");
                await File.WriteAllTextAsync(testFile, "test");

                // 确保文件被写入
                await Task.Delay(100);

                if (File.Exists(testFile))
                {
                    File.Delete(testFile);
                }

                _logger.LogDebug("驱动器 {Drive} 可用性检查通过 (尝试 {Attempt}/{MaxRetries})",
                    driveLetter, attempt, maxRetries);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogDebug("驱动器 {Drive} 可用性检查失败 (尝试 {Attempt}/{MaxRetries}): {Error}",
                    driveLetter, attempt, maxRetries, ex.Message);

                if (attempt < maxRetries)
                {
                    await Task.Delay(retryDelayMs);
                }
            }
        }

        _logger.LogWarning("驱动器 {Drive} 在 {MaxRetries} 次尝试后仍被占用", driveLetter, maxRetries);
        return true;
    }

    /// <summary>
    /// 检查当前进程是否以管理员权限运行
    /// </summary>
    private static bool IsRunningAsAdministrator()
    {
        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            return true; // 非Windows系统假设有足够权限

        try
        {
            using var identity = WindowsIdentity.GetCurrent();
            var principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }
        catch
        {
            return false;
        }
    }
}
